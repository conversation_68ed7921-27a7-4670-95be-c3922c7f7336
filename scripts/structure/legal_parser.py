import json
from dataclasses import dataclass
from typing import List
import re

@dataclass
class LegalArticle:
    article: str
    title: str
    content: str
    law: str

class LegalParser:
    def __init__(self, patterns):
        self.patterns = patterns
    
    def parse_article(self, text: str) -> LegalArticle:
        """Parse a single article"""
        article_match = re.search(self.patterns.ARTICLE_PATTERN, text)
        title_match = re.search(self.patterns.TITLE_PATTERN, text)
        
        return LegalArticle(
            article=article_match.group(0) if article_match else "UNKNOWN",
            title=title_match.group(1) if title_match else "",
            content=self._extract_content(text, title_match),
            law="Code Civil Tunisien"
        )
    
    def to_json(self, articles: List[LegalArticle], output_path: str):
        """Save structured data to JSON"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump([art.__dict__ for art in articles], f, ensure_ascii=False, indent=2)