import pdfplumber
from pathlib import Path

class PDFExtractor:
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
    
    def extract_text(self):
        """Extract raw text from PDF"""
        text = ""
        with pdfplumber.open(self.pdf_path) as pdf:
            for page in pdf.pages:
                text += page.extract_text()
        return text
    
    def save_raw_text(self, output_path):
        text = self.extract_text()
        Path(output_path).write_text(text, encoding='utf-8')
        return output_path