import re
from typing import List, Dict

class LegalTextCleaner:
    def __init__(self, patterns_config):
        self.patterns = patterns_config
    
    def clean_text(self, raw_text: str) -> str:
        """Apply cleaning patterns to raw text"""
        cleaned = raw_text
        for pattern, replacement in self.patterns.CLEANING_PATTERNS.items():
            cleaned = re.sub(pattern, replacement, cleaned)
        return cleaned
    
    def split_articles(self, cleaned_text: str) -> List[str]:
        """Split text into individual articles"""
        return re.split(self.patterns.ARTICLE_SPLIT_PATTERN, cleaned_text)