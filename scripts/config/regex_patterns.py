class CivilCodePatterns:
    # Example patterns - adjust based on your PDF
    ARTICLE_SPLIT_PATTERN = r'(?=Article\s+\d+)'
    ARTICLE_PATTERN = r'Article\s+\d+'
    TITLE_PATTERN = r'Article\s+\d+\s*-\s*(.*?)\n'
    
    CLEANING_PATTERNS = {
        r'\s+': ' ',          # Multiple spaces to single
        r'\x0c': '',          # Remove form feeds
        r'-\n': ''            # Join hyphenated words
    }