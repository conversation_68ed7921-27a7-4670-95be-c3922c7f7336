from extractors.pdf_extractor import PDFExtractor
from extractors.text_cleaner import LegalTextCleaner
from structure.legal_parser import LegalParser
from config.regex_patterns import CivilCodePatterns

def main():
    patterns = CivilCodePatterns()
    
    # Step 1: Extract raw text
    extractor = PDFExtractor("data/raw/tunisian_civil_code.pdf")
    raw_text = extractor.extract_text()
    
    # Step 2: Clean and split
    cleaner = LegalTextCleaner(patterns)
    cleaned = cleaner.clean_text(raw_text)
    articles = cleaner.split_articles(cleaned)
    
    # Step 3: Parse structure
    parser = LegalParser(patterns)
    structured = [parser.parse_article(art) for art in articles if art.strip()]
    
    # Step 4: Save to JSON
    parser.to_json(structured, "data/structured/civil_code_structured.json")

if __name__ == "__main__":
    main()