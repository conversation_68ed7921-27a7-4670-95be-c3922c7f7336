# legal-chatbot/
│
├── data/
│   ├── raw/                    # Original PDF files
│   │   └── tunisian_civil_code.pdf
│   ├── extracted/              # Extracted text files
│   │   └── civil_code_raw.txt
│   └── structured/             # Final structured data
│       └── civil_code_structured.json
│
├── docs/                       # Documentation
│   ├── specifications.md       # Project specifications
│   └── data_structure.md       # JSON schema documentation
│
├── notebooks/                  # Jupyter notebooks for exploration
│   └── pdf_extraction_experiments.ipynb
│
├── scripts/
│   ├── config/                 
│   │   └── regex_patterns.py   # Regex patterns for parsing
│   ├── extractors/             
│   │   ├── pdf_extractor.py    # Main extraction script
│   │   └── text_cleaner.py     # Text cleaning utilities
│   └── structure/              
│       └── legal_parser.py     # Structure parsing logic
│
├── tests/                      # Unit tests
│   ├── test_pdf_extraction.py
│   └── test_text_cleaning.py
│
├── requirements.txt            # Python dependencies
├── Makefile                    # Automation commands
├── LICENSE                     # Project license
└── README.md                   # Project overview